import { json } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { api } from "~/utils/api";

// 加載首頁數據
export async function loader() {
  try {
    const [recentUsage, recentBills, tariffPlans] = await Promise.all([
      api.getUsageRecords({ limit: 5 }),
      api.getBillingHistory({ limit: 5 }),
      api.getTariffPlans()
    ]);
    
    // 計算統計數據
    const stats = {
      totalUsage: recentUsage.reduce((sum: number, item: any) => sum + item.meterReading, 0),
      totalBills: recentBills.reduce((sum: number, bill: any) => sum + bill.totalAmount, 0),
      activeTariffs: tariffPlans.length
    };
    
    return json({ recentUsage, recentBills, stats });
  } catch (error) {
    console.error("加載首頁數據失敗:", error);
    throw new Response("加載首頁數據失敗", { status: 500 });
  }
}

export default function Index() {
  const { recentUsage, recentBills, stats } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      {/* 歡迎標題 */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">歡迎使用電費管理系統</h1>
        <p className="mt-2 text-gray-600">隨時掌握您的用電情況與電費支出</p>
      </div>
      
      {/* 快速操作按鈕 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link
          to="/usage"
          className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium">記錄用電</h3>
              <p className="text-sm text-gray-500">新增電表讀數</p>
            </div>
          </div>
        </Link>
        
        <Link
          to="/billing"
          className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium">計算電費</h3>
              <p className="text-sm text-gray-500">根據用電量計算電費</p>
            </div>
          </div>
        </Link>
        
        <Link
          to="/reports"
          className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium">查看報表</h3>
              <p className="text-sm text-gray-500">分析用電趨勢與統計</p>
            </div>
          </div>
        </Link>
      </div>
      
      {/* 統計卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">總用電量</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.totalUsage.toLocaleString('zh-TW')} <span className="text-sm text-gray-500">kWh</span>
              </p>
            </div>
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <Link to="/usage" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              查看所有記錄 →
            </Link>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">總電費支出</p>
              <p className="text-2xl font-semibold text-gray-900">
                NT$ {stats.totalBills.toLocaleString('zh-TW')}
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <Link to="/billing" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              查看所有帳單 →
            </Link>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">可用電價方案</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.activeTariffs} <span className="text-sm text-gray-500">種</span>
              </p>
            </div>
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <Link to="/tariffs" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              管理電價方案 →
            </Link>
          </div>
        </div>
      </div>
      
      {/* 最近記錄 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">最近用電記錄</h3>
            <Link to="/usage" className="text-sm text-blue-600 hover:text-blue-500">查看全部</Link>
          </div>
          <div className="space-y-4">
            {recentUsage?.length > 0 ? (
              recentUsage.map((record: any) => (
                <div key={record.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{new Date(record.date).toLocaleDateString('zh-TW')}</p>
                    <p className="text-sm text-gray-500">電表讀數: {record.meterReading} kWh</p>
                  </div>
                  <span className="text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                    {record.tariffPlan?.name || '未指定方案'}
                  </span>
                </div>
              ))
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">尚無用電記錄</p>
                <Link to="/usage" className="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-500">
                  新增用電記錄 →
                </Link>
              </div>
            )}
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">最近電費帳單</h3>
            <Link to="/billing" className="text-sm text-blue-600 hover:text-blue-500">查看全部</Link>
          </div>
          <div className="space-y-4">
            {recentBills?.length > 0 ? (
              recentBills.map((bill: any) => (
                <div key={bill.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">
                      {new Date(bill.startDate).toLocaleDateString('zh-TW')} - {
                        new Date(bill.endDate).toLocaleDateString('zh-TW')
                      }
                    </p>
                    <p className="text-sm text-gray-500">用電: {bill.usage} kWh</p>
                  </div>
                  <span className="text-lg font-semibold text-green-600">
                    NT$ {bill.totalAmount.toLocaleString('zh-TW')}
                  </span>
                </div>
              ))
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">尚無電費記錄</p>
                <Link to="/billing" className="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-500">
                  計算電費 →
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* 節能提示 */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-blue-800">節能小貼士</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>• 使用節能家電可以減少約 30% 的用電量</p>
              <p className="mt-1">• 空調溫度設定在 26-28°C 最為節能</p>
              <p className="mt-1">• 不使用的電器請拔掉插頭，避免待機耗電</p>
            </div>
            <div className="mt-4">
              <Link to="/tips" className="text-sm font-medium text-blue-700 hover:text-blue-600">
                查看更多節能建議 →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
