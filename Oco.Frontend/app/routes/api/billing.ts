import { json, type ActionFunctionArgs } from "@remix-run/node";
import { api } from "~/utils/api";

// 處理 POST 請求 - 計算電費
export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const { usageId, tariffPlanId } = Object.fromEntries(formData);
    
    // 驗證數據
    if (!usageId || !tariffPlanId) {
      return json(
        { error: "請選擇用電記錄和電價方案" },
        { status: 400 }
      );
    }
    
    // 調用後端 API 計算電費
    const result = await api.calculateBill(
      usageId.toString(),
      tariffPlanId.toString()
    );
    
    return json(result);
    
  } catch (error) {
    console.error("計算電費失敗:", error);
    return json(
      { error: "計算電費時發生錯誤" },
      { status: 500 }
    );
  }
}

// 處理 GET 請求 - 獲取電費歷史記錄
export async function loader() {
  try {
    const billingHistory = await api.getBillingHistory();
    return json(billingHistory);
  } catch (error) {
    console.error("獲取電費歷史記錄失敗:", error);
    return json(
      { error: "獲取電費歷史記錄時發生錯誤" },
      { status: 500 }
    );
  }
}
