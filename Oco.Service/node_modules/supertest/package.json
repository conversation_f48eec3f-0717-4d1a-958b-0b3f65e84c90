{"name": "supertest", "description": "SuperAgent driven library for testing HTTP servers", "version": "7.1.4", "author": "<PERSON><PERSON>", "contributors": [], "dependencies": {"methods": "^1.1.2", "superagent": "^10.2.3"}, "devDependencies": {"@commitlint/cli": "17", "@commitlint/config-conventional": "17", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "eslint": "^8.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "express": "^4.18.3", "mocha": "^10.2.0", "nock": "^13.3.8", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "should": "^13.2.3"}, "engines": {"node": ">=14.18.0"}, "files": ["index.js", "lib"], "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/ladjs/supertest.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint": "eslint lib/**/*.js test/**/*.js index.js", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js", "pretest": "npm run lint --if-present", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks"}}