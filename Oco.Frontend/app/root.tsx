import {
  <PERSON>s,
  <PERSON>a,
  <PERSON>let,
  <PERSON><PERSON>ts,
  ScrollRestoration,
  useLoaderData,
  useLocation,
  Link,
  NavLink,
} from "@remix-run/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useTranslation } from "react-i18next";
import { useChangeLanguage } from "remix-i18next/react";
import { useMemo } from "react";
import { i18next } from "./i18n/i18n.server";
import stylesheet from "./tailwind.css";

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: stylesheet },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  { rel: "preconnect", href: "https://fonts.gstatic.com", crossOrigin: "anonymous" },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap",
  },
];

// 在服務器端加載語言
// 這將在服務器端運行，並將語言信息傳遞給客戶端
export async function loader({ request }: LoaderFunctionArgs) {
  const locale = i18next.language;
  return json({ locale });
}

// 創建一個單例 QueryClient 實例
let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient() {
  if (typeof window === "undefined") {
    // 服務器端：總是創建新的 QueryClient
    return new QueryClient({
      defaultOptions: {
        queries: {
          // 服務器端查詢不應該重試，因為我們希望立即顯示錯誤
          retry: false,
        },
      },
    });
  }
  // 瀏覽器端：使用單例模式避免在重新渲染時丟失緩存
  if (!browserQueryClient) {
    browserQueryClient = new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 5 * 60 * 1000, // 5 分鐘
          refetchOnWindowFocus: false,
          retry: 1,
        },
      },
    });
  }
  return browserQueryClient;
}

export function Layout({ children }: { children: React.ReactNode }) {
  // 獲取服務器端設置的語言
  const { locale } = useLoaderData<typeof loader>();
  const { i18n: i18nClient } = useTranslation();
  
  // 當語言變化時更新 i18next 實例
  useChangeLanguage(locale);
  
  // 獲取 QueryClient 實例
  const queryClient = useMemo(() => getQueryClient(), []);
  
  // 獲取當前路徑
  const location = useLocation();
  const { t } = useTranslation('common');
  
  // 導航鏈接
  const navLinks = [
    { to: "/", label: t('navigation.dashboard') },
    { to: "/usage", label: t('navigation.usage') },
    { to: "/billing", label: t('navigation.billing') },
    { to: "/reports", label: t('navigation.reports') },
    { to: "/tariffs", label: t('navigation.tariffs') },
  ];

  return (
    <html lang={locale} dir={i18nClient.dir()}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="description" content={t('app.description')} />
        <Meta />
        <Links />
      </head>
      <body className="min-h-screen bg-gray-50 font-sans antialiased">
        <QueryClientProvider client={queryClient}>
          <div className="min-h-screen flex flex-col">
            {/* 導航欄 */}
            <header className="bg-white shadow-sm">
              <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16">
                  <div className="flex items-center">
                    <Link to="/" className="flex-shrink-0 flex items-center">
                      <h1 className="text-xl font-bold text-gray-900">
                        {t('app.name')}
                      </h1>
                    </Link>
                    <nav className="hidden md:ml-10 md:flex space-x-8">
                      {navLinks.map((link) => (
                        <NavLink
                          key={link.to}
                          to={link.to}
                          className={({ isActive }) =>
                            `inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                              isActive
                                ? 'border-blue-500 text-gray-900'
                                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                            }`
                          }
                        >
                          {link.label}
                        </NavLink>
                      ))}
                    </nav>
                  </div>
                  
                  <div className="hidden md:flex items-center space-x-4">
                    {/* 語言切換 */}
                    <div className="relative">
                      <select
                        value={locale}
                        onChange={(e) => {
                          const newLocale = e.target.value;
                          // 這裡可以添加切換語言的邏輯
                          window.location.href = `/${newLocale}${location.pathname.replace(/^\/[a-z]{2}(-[A-Z]{2})?\//, '/')}`;
                        }}
                        className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      >
                        <option value="zh-TW">繁體中文</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                    
                    {/* 用戶菜單 */}
                    <div className="ml-4 flex items-center">
                      <button className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span className="sr-only">View notifications</span>
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                      </button>
                      
                      <div className="ml-3 relative">
                        <div>
                          <button type="button" className="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                            <span className="sr-only">Open user menu</span>
                            <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                              {t('navigation.profile').charAt(0).toUpperCase()}
                            </div>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 移動端菜單按鈕 */}
                  <div className="-mr-2 flex items-center md:hidden">
                    <button type="button" className="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" aria-controls="mobile-menu" aria-expanded="false">
                      <span className="sr-only">Open main menu</span>
                      <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                      </svg>
                      <svg className="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              
              {/* 移動端菜單 */}
              <div className="md:hidden" id="mobile-menu">
                <div className="pt-2 pb-3 space-y-1">
                  {navLinks.map((link) => (
                    <NavLink
                      key={link.to}
                      to={link.to}
                      className={({ isActive }) =>
                        `block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${
                          isActive
                            ? 'bg-blue-50 border-blue-500 text-blue-700'
                            : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'
                        }`
                      }
                    >
                      {link.label}
                    </NavLink>
                  ))}
                </div>
                <div className="pt-4 pb-3 border-t border-gray-200">
                  <div className="flex items-center px-4">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                        {t('navigation.profile').charAt(0).toUpperCase()}
                      </div>
                    </div>
                    <div className="ml-3">
                      <div className="text-base font-medium text-gray-800">
                        {t('navigation.profile')}
                      </div>
                    </div>
                  </div>
                  <div className="mt-3 space-y-1">
                    <a href="#" className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">
                      {t('navigation.settings')}
                    </a>
                    <a href="#" className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">
                      {t('navigation.logout')}
                    </a>
                  </div>
                </div>
              </div>
            </header>

            {/* 主內容區域 */}
            <main className="flex-grow">
              <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
                {children}
              </div>
            </main>

            {/* 頁腳 */}
            <footer className="bg-white border-t border-gray-200 mt-8">
              <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div className="md:flex md:items-center md:justify-between">
                  <div className="flex justify-center md:justify-start space-x-6">
                    <a href="#" className="text-gray-400 hover:text-gray-500">
                      <span className="sr-only">GitHub</span>
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.578.688.48A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                      </svg>
                    </a>
                  </div>
                  <div className="mt-8 md:mt-0">
                    <p className="text-center text-base text-gray-400">
                      &copy; {new Date().getFullYear()} {t('app.name')}. {t('common.allRightsReserved')}.
                    </p>
                  </div>
                </div>
              </div>
            </footer>
          </div>
        </QueryClientProvider>
        
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}
