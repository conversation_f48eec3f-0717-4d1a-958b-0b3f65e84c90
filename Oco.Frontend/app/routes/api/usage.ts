import { json, type ActionFunctionArgs } from "@remix-run/node";
import { api } from "~/utils/api";

// 處理 POST 請求 - 新增用電記錄
export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData);
    
    // 驗證數據
    if (!data.date || !data.meterReading) {
      return json(
        { error: "請填寫所有必填欄位" },
        { status: 400 }
      );
    }
    
    // 轉換數據類型
    const usageRecord = {
      date: data.date.toString(),
      meterReading: parseFloat(data.meterReading.toString()),
      tariffPlanId: data.tariffPlanId?.toString() || "",
      notes: data.notes?.toString() || ""
    };
    
    // 調用後端 API
    const result = await api.createUsageRecord(usageRecord);
    return json(result);
    
  } catch (error) {
    console.error("新增用電記錄失敗:", error);
    return json(
      { error: "新增用電記錄時發生錯誤" },
      { status: 500 }
    );
  }
}

// 處理 GET 請求 - 獲取用電記錄
export async function loader() {
  try {
    const usageData = await api.getUsageRecords();
    return json(usageData);
  } catch (error) {
    console.error("獲取用電記錄失敗:", error);
    return json(
      { error: "獲取用電記錄時發生錯誤" },
      { status: 500 }
    );
  }
}
