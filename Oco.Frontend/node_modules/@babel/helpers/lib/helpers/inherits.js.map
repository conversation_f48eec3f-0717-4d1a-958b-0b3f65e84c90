{"version": 3, "names": ["_setPrototypeOf", "require", "_inherits", "subClass", "superClass", "TypeError", "prototype", "Object", "create", "constructor", "value", "writable", "configurable", "defineProperty", "setPrototypeOf"], "sources": ["../../src/helpers/inherits.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport setPrototypeOf from \"./setPrototypeOf.ts\";\n\nexport default function _inherits(\n  subClass: Function,\n  superClass: Function | null,\n) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  // We can't use defineProperty to set the prototype in a single step because it\n  // doesn't work in Chrome <= 36. https://github.com/babel/babel/issues/14056\n  // V8 bug: https://bugs.chromium.org/p/v8/issues/detail?id=3334\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true,\n    },\n  });\n  Object.defineProperty(subClass, \"prototype\", { writable: false });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AAEe,SAASC,SAASA,CAC/BC,QAAkB,EAClBC,UAA2B,EAC3B;EACA,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAIC,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAIAF,QAAQ,CAACG,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,UAAU,IAAIA,UAAU,CAACE,SAAS,EAAE;IACrEG,WAAW,EAAE;MACXC,KAAK,EAAEP,QAAQ;MACfQ,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACFL,MAAM,CAACM,cAAc,CAACV,QAAQ,EAAE,WAAW,EAAE;IAAEQ,QAAQ,EAAE;EAAM,CAAC,CAAC;EACjE,IAAIP,UAAU,EAAE,IAAAU,uBAAc,EAACX,QAAQ,EAAEC,UAAU,CAAC;AACtD", "ignoreList": []}