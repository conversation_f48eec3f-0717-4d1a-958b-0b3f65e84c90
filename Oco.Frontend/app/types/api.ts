export interface ApiClient {
  getUsageRecords(): Promise<UsageRecord[]>;
  getTariffPlans(): Promise<TariffPlan[]>;
  getBillingHistory(): Promise<BillingRecord[]>;
  post<T = any>(url: string, data: any): Promise<{ data: T }>;
  calculateBill(usageRecordId: string, tariffPlanId: string): Promise<BillingCalculation>;
}

export interface UsageRecord {
  id: string;
  date: string;
  meterReading: number;
  tariffPlanId: string;
  notes?: string;
}

export interface TariffPlan {
  id: string;
  name: string;
  rate: number;
  rates: Array<{
    from: number;
    to?: number;
    rate: number;
  }>;
  effectiveDate: string;
  expirationDate?: string;
}

export interface BillingRecord {
  id: string;
  usageRecordId: string;
  tariffPlanId: string;
  amount: number;
  billingDate: string;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue';
  calculatedAt: string;
  startDate: string;
  endDate: string;
  usage: number;
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
}

export interface BillingCalculation {
  amount: number;
  details: {
    usage: number;
    rate: number;
    subtotal: number;
    tax: number;
    total: number;
  };
  startDate?: string;
  endDate?: string;
  tariffPlanId?: string;
  usage?: number;
  totalAmount?: number;
}
