import type { BillingCalculation } from "~/types/api";

declare global {
  interface Window {
    api: {
      getUsageRecords: () => Promise<any[]>;
      getTariffPlans: () => Promise<any[]>;
      getBillingHistory: () => Promise<any[]>;
      calculateBill: (usageRecordId: string, tariffPlanId: string) => Promise<BillingCalculation>;
      createBill: (data: any) => Promise<any>;
    };
  }
}

export {};
