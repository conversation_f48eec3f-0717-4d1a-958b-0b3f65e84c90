import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useState } from "react";
import { api } from "~/utils/api";

// 加載報表數據
export async function loader() {
  try {
    const reports = await api.generateReport({
      period: 'monthly',
      year: new Date().getFullYear()
    });
    return json({ reports });
  } catch (error) {
    console.error("加載報表數據失敗:", error);
    throw new Response("加載報表數據失敗", { status: 500 });
  }
}

export default function ReportsPage() {
  const { reports } = useLoaderData<typeof loader>();
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  
  // 生成年份選項（最近5年）
  const years = [];
  const currentYear = new Date().getFullYear();
  for (let year = currentYear; year >= currentYear - 4; year--) {
    years.push(year);
  }

  // 處理篩選條件變更
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === 'year') {
      setSelectedYear(parseInt(value, 10));
    } else if (name === 'period') {
      setSelectedPeriod(value);
    }
    // 這裡可以添加重新加載數據的邏輯
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-800">用電與電費報表</h2>
        <div className="flex space-x-4">
          <select
            name="period"
            value={selectedPeriod}
            onChange={handleFilterChange}
            className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="daily">日報表</option>
            <option value="weekly">週報表</option>
            <option value="monthly">月報表</option>
            <option value="yearly">年報表</option>
          </select>
          <select
            name="year"
            value={selectedYear}
            onChange={handleFilterChange}
            className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            {years.map(year => (
              <option key={year} value={year}>{year}年</option>
            ))}
          </select>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            匯出報表
          </button>
        </div>
      </div>
      
      {/* 統計卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">本月用電量</p>
              <p className="text-2xl font-semibold text-gray-900">
                {reports?.currentMonth?.usage || 0} <span className="text-sm text-gray-500">kWh</span>
              </p>
              <p className="text-sm text-green-600">
                {reports?.usageChange?.value && reports.usageChange.value > 0 ? '↑' : '↓'} {Math.abs(reports?.usageChange?.value || 0)}% 相較上月
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">本月電費</p>
              <p className="text-2xl font-semibold text-gray-900">
                NT$ {reports?.currentMonth?.amount ? reports.currentMonth.amount.toLocaleString('zh-TW') : '0'}
              </p>
              <p className="text-sm text-green-600">
                {reports?.amountChange?.value && reports.amountChange.value > 0 ? '↑' : '↓'} {Math.abs(reports?.amountChange?.value || 0)}% 相較上月
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">平均每日用電</p>
              <p className="text-2xl font-semibold text-gray-900">
                {reports?.averageDailyUsage?.toFixed(2) || '0.00'} <span className="text-sm text-gray-500">kWh/日</span>
              </p>
              <p className="text-sm text-green-600">
                較去年同期 {reports?.yearlyComparison?.value && reports.yearlyComparison.value > 0 ? '增加' : '減少'} {Math.abs(reports?.yearlyComparison?.value || 0)}%
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">年度用電趨勢</p>
              <p className="text-2xl font-semibold text-gray-900">
                {reports?.yearlyTrend?.direction === 'up' ? '上升' : '下降'}
              </p>
              <p className="text-sm text-green-600">
                較去年 {reports?.yearlyTrend?.percentage || 0}%
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* 用電量趨勢圖表 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">用電量趨勢</h3>
          <div className="flex space-x-2">
            <button className="px-3 py-1 text-sm rounded-md bg-blue-100 text-blue-700">日</button>
            <button className="px-3 py-1 text-sm rounded-md bg-white border border-gray-300">週</button>
            <button className="px-3 py-1 text-sm rounded-md bg-white border border-gray-300">月</button>
            <button className="px-3 py-1 text-sm rounded-md bg-white border border-gray-300">年</button>
          </div>
        </div>
        <div className="h-64 bg-gray-50 rounded-md flex items-center justify-center">
          <p className="text-gray-500">用電量趨勢圖表將顯示在此處</p>
          {/* 這裡可以整合圖表庫，如 Chart.js 或 Recharts */}
        </div>
      </div>
      
      {/* 電費分佈 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium mb-4">電費分佈</h3>
          <div className="h-64 bg-gray-50 rounded-md flex items-center justify-center">
            <p className="text-gray-500">電費分佈圓餅圖將顯示在此處</p>
            {/* 這裡可以整合圖表庫 */}
          </div>
          <div className="mt-4 space-y-2">
            {reports?.costDistribution?.map((item: any) => (
              <div key={item.category} className="flex justify-between">
                <div className="flex items-center">
                  <div 
                    className="w-3 h-3 rounded-full mr-2" 
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-sm text-gray-700">{item.category}</span>
                </div>
                <span className="text-sm font-medium">{item.percentage}%</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium mb-4">用電時段分析</h3>
          <div className="h-64 bg-gray-50 rounded-md flex items-center justify-center">
            <p className="text-gray-500">用電時段分析圖表將顯示在此處</p>
            {/* 這裡可以整合圖表庫 */}
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span>離峰時段 (00:00-07:00)</span>
              <span className="font-medium">{reports?.timeOfUse?.offPeak || 0}%</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>尖峰時段 (07:00-22:00)</span>
              <span className="font-medium">{reports?.timeOfUse?.peak || 0}%</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>半尖峰時段 (22:00-24:00)</span>
              <span className="font-medium">{reports?.timeOfUse?.semiPeak || 0}%</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* 詳細數據表格 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">詳細數據</h3>
          <button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200">
            下載 CSV
          </button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  日期
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用電量 (kWh)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  電費 (NT$)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  平均單價 (NT$/kWh)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  尖峰用電 (kWh)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  離峰用電 (kWh)
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {reports?.detailedData?.map((item: any) => (
                <tr key={item.date} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.usage.toLocaleString('zh-TW')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.cost.toLocaleString('zh-TW')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.averagePrice.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.peakUsage.toLocaleString('zh-TW')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.offPeakUsage.toLocaleString('zh-TW')}
                  </td>
                </tr>
              ))}
              {(!reports?.detailedData || reports.detailedData.length === 0) && (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    沒有可用的報表數據
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
