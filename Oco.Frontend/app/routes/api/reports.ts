import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { api } from "~/utils/api";

// 處理 GET 請求 - 獲取報表數據
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || 'monthly';
    const year = parseInt(url.searchParams.get('year') || new Date().getFullYear().toString(), 10);
    
    // 模擬報表數據（實際項目中應該從後端獲取）
    const mockReports = {
      currentMonth: {
        usage: 320,
        amount: 1250
      },
      usageChange: {
        value: 5.2,
        isIncrease: true
      },
      amountChange: {
        value: 3.8,
        isIncrease: true
      },
      averageDailyUsage: 10.5,
      yearlyComparison: {
        value: 2.4,
        isIncrease: false
      },
      yearlyTrend: {
        direction: 'down',
        percentage: 2.4
      },
      costDistribution: [
        { category: '基本電費', value: 45, color: '#3b82f6' },
        { category: '流動電費', value: 35, color: '#10b981' },
        { category: '燃料費', value: 15, color: '#f59e0b' },
        { category: '再生能源發展基金', value: 5, color: '#6366f1' }
      ],
      timeOfUse: {
        peak: 60,
        offPeak: 30,
        semiPeak: 10
      },
      detailedData: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(year, i, 1).toLocaleDateString('zh-TW', { month: 'long' }),
        usage: Math.floor(Math.random() * 500) + 100,
        cost: Math.floor(Math.random() * 2000) + 500,
        averagePrice: (Math.random() * 5 + 2.5).toFixed(2),
        peakUsage: Math.floor(Math.random() * 300) + 50,
        offPeakUsage: Math.floor(Math.random() * 200) + 30
      }))
    };
    
    return json(mockReports);
    
  } catch (error) {
    console.error("獲取報表數據失敗:", error);
    return json(
      { error: "獲取報表數據時發生錯誤" },
      { status: 500 }
    );
  }
}
