import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderD<PERSON>, useFetcher, useNavigation } from "@remix-run/react";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { zhTW } from 'date-fns/locale';
import type { BillingCalculation } from "~/types/api";

// API 客戶端
const api = {
  async getUsageRecords(): Promise<UsageRecord[]> {
    const response = await fetch('/api/usage');
    if (!response.ok) throw new Error('Failed to fetch usage records');
    return response.json();
  },
  
  async getTariffPlans(): Promise<TariffPlan[]> {
    const response = await fetch('/api/tariffs');
    if (!response.ok) throw new Error('Failed to fetch tariff plans');
    return response.json();
  },
  
  async getBillingHistory(): Promise<BillingRecord[]> {
    const response = await fetch('/api/billing');
    if (!response.ok) throw new Error('Failed to fetch billing history');
    return response.json();
  },
  
  async calculateBill(usageRecordId: string, tariffPlanId: string): Promise<BillingCalculation> {
    const response = await fetch('/api/billing/calculate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ usageRecordId, tariffPlanId })
    });
    if (!response.ok) throw new Error('Failed to calculate bill');
    return response.json();
  },
  
  async createBill(data: Omit<BillingRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<BillingRecord> {
    const response = await fetch('/api/billing', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to create bill');
    return response.json();
  }
};

// 定義類型
type UsageRecord = {
  id: string;
  date: string;
  meterReading: number;
  tariffPlanId: string;
};

type TariffPlan = {
  id: string;
  name: string;
  rates: Array<{
    from: number;
    to?: number;
    rate: number;
  }>;
  effectiveDate: string;
  expirationDate?: string;
};

type BillingRecord = {
  id: string;
  usageRecordId: string;
  tariffPlanId: string;
  amount: number;
  billingDate: string;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue';
  startDate: string;
  endDate: string;
  usage: number;
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
};

type LoaderData = {
  usageData: UsageRecord[];
  tariffPlans: TariffPlan[];
  billingHistory: BillingRecord[];
};

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const [usageData, tariffPlans, billingHistory] = await Promise.all([
      api.getUsageRecords(),
      api.getTariffPlans(),
      api.getBillingHistory()
    ]);
    
    return json<LoaderData>({ 
      usageData, 
      tariffPlans, 
      billingHistory 
    });
  } catch (error) {
    console.error('Loader error:', error);
    throw new Response("加載數據失敗", { status: 500 });
  }
}

export default function BillingPage() {
  // 從 loader 獲取初始數據
  const { usageData = [], tariffPlans = [], billingHistory: initialBillingHistory = [] } = useLoaderData<typeof loader>();
  const [selectedUsage, setSelectedUsage] = useState("");
  const [selectedTariff, setSelectedTariff] = useState("");
  const [calculationResult, setCalculationResult] = useState<BillingCalculation | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [billingHistory, setBillingHistory] = useState<BillingRecord[]>(initialBillingHistory);
  const fetcher = useFetcher();
  const navigation = useNavigation();
  
  // 格式化日期
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'yyyy/MM/dd', { locale: zhTW });
  };
  
  // 獲取電價方案名稱
  const getTariffName = (id?: string): string => {
    if (!id) return "未知方案";
    const plan = tariffPlans.find((p) => p.id === id);
    return plan?.name || "未知方案";
  };

  // 當選擇用電記錄時，自動選擇對應的電價方案
  useEffect(() => {
    if (selectedUsage && usageData) {
      const selectedRecord = usageData.find(r => r.id === selectedUsage);
      if (selectedRecord) {
        setSelectedTariff(selectedRecord.tariffPlanId);
      }
    }
  }, [selectedUsage, usageData, setSelectedTariff]);

  // 計算電費
  const calculateBill = async (): Promise<void> => {
    if (!selectedUsage || !selectedTariff) return;
    
    try {
      const result = await api.calculateBill(selectedUsage, selectedTariff);
      setCalculationResult({
        ...result,
        startDate: result.startDate || new Date().toISOString(),
        endDate: result.endDate || new Date().toISOString(),
        usage: result.usage || 0,
        totalAmount: result.totalAmount || 0
      });
    } catch (error) {
      console.error("計算電費失敗:", error);
      alert("計算電費時發生錯誤，請稍後再試");
    }
  };
  
  // 處理表單提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    calculateBill().catch(console.error);
  };

  useEffect(() => {
    if (selectedUsage && selectedTariff) {
      calculateBill().catch(console.error);
    }
  }, [selectedUsage, selectedTariff]);

  // 處理生成帳單
  const handleGenerateBill = async () => {
    if (!selectedUsage || !selectedTariff || !calculationResult) {
      console.error('Missing required data for bill generation');
      return;
    }
    
    try {
      // 確保所有必要的欄位都有預設值
      const billData = {
        usageRecordId: selectedUsage,
        tariffPlanId: selectedTariff,
        status: 'pending' as const,
        billingDate: new Date().toISOString(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        startDate: calculationResult.startDate || new Date().toISOString(),
        endDate: calculationResult.endDate || new Date().toISOString(),
        usage: calculationResult.usage || 0,
        totalAmount: calculationResult.totalAmount || 0,
        amount: calculationResult.totalAmount || 0,
        // 添加其他必要的預設值
        id: '', // 由後端生成
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      await api.createBill(billData);
      
      alert('帳單已成功生成！');
      // 重載頁面以更新帳單歷史
      window.location.reload();
    } catch (error) {
      console.error("生成帳單失敗:", error);
      alert("生成帳單時發生錯誤，請稍後再試");
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-gray-800">電費計算</h2>
      
      {/* 電費計算表單 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">計算電費</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              選擇用電記錄
            </label>
            <select
              value={selectedUsage}
              onChange={(e) => setSelectedUsage(e.target.value)}
              className="w-full p-2 border rounded"
              aria-label="選擇用電記錄"
              title="選擇用電記錄"
            >
              <option value="">-- 請選擇用電記錄 --</option>
              {usageData.map((record, index) => {
                const prevReading = index > 0 ? usageData[index - 1]?.meterReading : 0;
                const usage = (record.meterReading - prevReading).toFixed(2);
                return (
                  <option key={record.id} value={record.id}>
                    {new Date(record.date).toLocaleDateString('zh-TW')} - {usage} kWh
                  </option>
                );
              })}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">
              選擇電價方案
            </label>
            <select
              value={selectedTariff}
              onChange={(e) => setSelectedTariff(e.target.value)}
              className="w-full p-2 border rounded"
              aria-label="選擇電價方案"
              title="選擇電價方案"
            >
              <option value="">-- 請選擇電價方案 --</option>
              {tariffPlans.map((plan) => (
                <option key={plan.id} value={plan.id}>
                  {plan.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <button
              type="submit"
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={!selectedUsage || !selectedTariff}
            >
              計算電費
            </button>
          </div>
        </form>
        
        {/* 計算結果 */}
        {calculationResult && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-lg font-medium text-gray-900 mb-2">計算結果</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">用電期間</p>
                <p className="font-medium">
                  <p>用電量: {calculationResult.details.usage} 度</p>
                  <p>費率: ${calculationResult.details.rate} / 度</p>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">用電度數</p>
                <p className="font-medium">{calculationResult.usage} 度</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">電價方案</p>
                <p className="font-medium">{getTariffName(calculationResult.tariffPlanId)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">應付電費</p>
                <p className="text-2xl font-bold text-blue-600">
                  <p>小計: ${calculationResult.details.subtotal.toFixed(2)}</p>
                  <p>稅金: ${calculationResult.details.tax.toFixed(2)}</p>
                  <p>總金額: ${calculationResult.details.total.toFixed(2)}</p>
                </p>
              </div>
              {calculationResult.details && (
                <div className="col-span-full">
                  <p className="text-sm text-gray-600 mb-1">費用明細</p>
                  <div className="border rounded-md divide-y">
                    <div className="p-2 border-b">
                      <p>費率: ${calculationResult.details.rate} / 度</p>
                      <p>用電量: {calculationResult.details.usage} 度</p>
                      <p>小計: ${calculationResult.details.subtotal.toFixed(2)}</p>
                      <p>稅金 (5%): ${calculationResult.details.tax.toFixed(2)}</p>
                      <p>總金額: ${calculationResult.details.total.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="mt-4">
              <button
                onClick={() => {
                  // 這裡可以添加保存計算結果的邏輯
                  alert('已保存計算結果');
                }}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                保存此計算結果
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* 歷史電費記錄 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">歷史電費記錄</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  計算日期
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用電期間
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用電度數 (kWh)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  電價方案
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  應付電費 (NT$)
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {billingHistory.map((bill) => (
                <tr key={bill.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {bill.billingDate ? new Date(bill.billingDate).toLocaleDateString('zh-TW') : ''}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {bill.startDate ? new Date(bill.startDate).toLocaleDateString('zh-TW') : ''} - 
                    {bill.endDate ? new Date(bill.endDate).toLocaleDateString('zh-TW') : ''}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {(bill.usage || 0).toFixed(2)} 度
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {getTariffName(bill.tariffPlanId || '')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    NT$ {(bill.totalAmount || 0).toLocaleString('zh-TW')}
                  </td>
                </tr>
              ))}
              {(!billingHistory || billingHistory.length === 0) && (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    尚無歷史電費記錄
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
