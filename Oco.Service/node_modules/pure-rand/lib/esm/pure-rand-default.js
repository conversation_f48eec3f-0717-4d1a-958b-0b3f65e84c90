import { congruential32 } from './generator/LinearCongruential.js';
import { mersenne } from './generator/MersenneTwister.js';
import { xorshift128plus } from './generator/XorShift.js';
import { xoroshiro128plus } from './generator/XoroShiro.js';
import { uniformArrayIntDistribution } from './distribution/UniformArrayIntDistribution.js';
import { uniformBigIntDistribution } from './distribution/UniformBigIntDistribution.js';
import { uniformIntDistribution } from './distribution/UniformIntDistribution.js';
import { unsafeUniformArrayIntDistribution } from './distribution/UnsafeUniformArrayIntDistribution.js';
import { unsafeUniformBigIntDistribution } from './distribution/UnsafeUniformBigIntDistribution.js';
import { unsafeUniformIntDistribution } from './distribution/UnsafeUniformIntDistribution.js';
import { skipN } from './distribution/SkipN.js';
import { generateN } from './distribution/GenerateN.js';
import { unsafeGenerateN } from './distribution/UnsafeGenerateN.js';
import { unsafeSkipN } from './distribution/UnsafeSkipN.js';
var __type = 'module';
var __version = '7.0.1';
var __commitHash = '2248506b66d969d1a8b477a4dde8e24cbac33e6a';
export { __type, __version, __commitHash, generateN, skipN, unsafeGenerateN, unsafeSkipN, congruential32, mersenne, xorshift128plus, xoroshiro128plus, uniformArrayIntDistribution, uniformBigIntDistribution, uniformIntDistribution, unsafeUniformArrayIntDistribution, unsafeUniformBigIntDistribution, unsafeUniformIntDistribution, };
