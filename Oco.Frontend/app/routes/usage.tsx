import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderD<PERSON>, useFetcher, useNavigation } from "@remix-run/react";
import { useEffect, useState } from "react";
import { api } from "~/utils/api";
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ChartData
} from 'chart.js';

// 定義類型
interface TariffPlan {
  id: string;
  name: string;
  description: string;
  rates: Array<{
    from: number;
    to: number;
    rate: number;
  }>;
  effectiveDate: string;
  expirationDate?: string;
  isActive: boolean;
}

interface UsageRecord {
  id: string;
  date: string;
  meterReading: number;
  tariffPlanId: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface LoaderData {
  usageData: UsageRecord[];
  tariffPlans: TariffPlan[];
}

interface FormData {
  date: string;
  meterReading: string;
  tariffPlanId: string;
  notes: string;
}

// 註冊 ChartJS 組件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

// 加載用電量數據
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const [usageData, tariffPlans] = await Promise.all([
      api.getUsageRecords(),
      api.getTariffPlans()
    ]);
    
    return json<LoaderData>({ 
      usageData: Array.isArray(usageData) ? usageData : [], 
      tariffPlans: Array.isArray(tariffPlans) ? tariffPlans : [] 
    });
  } catch (error) {
    console.error("加載數據失敗:", error);
    throw new Response("加載數據失敗", { status: 500 });
  }
}

export default function UsagePage() {
  const { usageData, tariffPlans } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<{ success?: boolean; error?: string }>();
  const navigation = useNavigation();
  const [formData, setFormData] = useState<FormData>({
    date: new Date().toISOString().split('T')[0],
    meterReading: "",
    tariffPlanId: tariffPlans[0]?.id || "",
    notes: ""
  });
  
  // 準備圖表數據
  const chartData: ChartData<'line'> = {
    labels: usageData.map(record => new Date(record.date).toLocaleDateString()),
    datasets: [
      {
        label: '用電度數',
        data: usageData.map(record => record.meterReading),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
        fill: false
      }
    ]
  };
  
  const chartOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: '用電量趨勢圖',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: '度 (kWh)'
        }
      },
      x: {
        title: {
          display: true,
          text: '日期'
        }
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const formDataObj = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      formDataObj.append(key, String(value));
    });
    
    fetcher.submit(
      formDataObj,
      { 
        method: "post", 
        action: "/api/usage",
        encType: "application/x-www-form-urlencoded"
      }
    );
  };

  // 提交表單後重置表單
  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data?.success) {
      setFormData({
        date: new Date().toISOString().split('T')[0],
        meterReading: "",
        tariffPlanId: tariffPlans[0]?.id || "",
        notes: ""
      });
    }
  }, [fetcher.state, fetcher.data, tariffPlans]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-800">用電量管理</h2>
      </div>
      
      {/* 用電量圖表 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">用電量趨勢</h3>
        {usageData.length > 0 ? (
          <div className="h-80">
            <Line data={chartData} options={chartOptions} />
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">暫無數據，請新增用電記錄</p>
        )}
      </div>
      
      {/* 新增用電記錄表單 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">新增用電記錄</h3>
        {fetcher.data?.error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
            {fetcher.data.error}
          </div>
        )}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">日期</label>
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
                max={new Date().toISOString().split('T')[0]}
                aria-label="選擇日期"
                title="請選擇記錄日期"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">電表讀數 (度)</label>
              <input
                type="number"
                id="meterReading"
                name="meterReading"
                value={formData.meterReading}
                onChange={handleChange}
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="請輸入電表讀數"
                required
                aria-label="電表讀數"
                title="請輸入電表讀數 (度)"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">電價方案</label>
            <select
              id="tariffPlanId"
              name="tariffPlanId"
              value={formData.tariffPlanId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              required
              aria-label="選擇電價方案"
              title="請選擇適用的電價方案"
            >
              {tariffPlans?.map(plan => (
                <option key={plan.id} value={plan.id}>
                  {plan.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">備註 (選填)</label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="可輸入特殊情況或說明"
              aria-label="備註"
              title="可選的額外說明"
            />
          </div>
          <button
            type="submit"
            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={fetcher.state === "submitting"}
          >
            {fetcher.state === "submitting" ? "儲存中..." : "儲存記錄"}
          </button>
        </form>
      </div>
      
      {/* 用電記錄列表 */}
      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">歷史記錄</h3>
        {usageData.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">電表讀數 (度)</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">電價方案</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">備註</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {usageData.map((record: UsageRecord) => (
                  <tr key={record.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(record.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {record.meterReading.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {tariffPlans.find((p: TariffPlan) => p.id === record.tariffPlanId)?.name || '未知'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {record.notes || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">暫無用電記錄</p>
        )}
      </div>
    </div>
  );
}
