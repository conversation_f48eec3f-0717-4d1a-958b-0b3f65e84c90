// app/entry.server.tsx
import { PassThrough } from "node:stream";
import { Response } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import isbot from "isbot";
import { renderToPipeableStream } from "react-dom/server";
import { createInstance as createInstance2 } from "i18next";
import { I18nextProvider, initReactI18next as initReactI18next2 } from "react-i18next";
import Backend2 from "i18next-fs-backend";
import { resolve as resolve2 } from "node:path";

// app/i18n/i18n.server.ts
import { createInstance } from "i18next";
import { initReactI18next } from "react-i18next";
import Backend from "i18next-fs-backend";
import { resolve } from "node:path";
import { fileURLToPath } from "node:url";
import { dirname } from "node:path";
import { RemixI18Next } from "remix-i18next/server";
import { createCookie } from "@remix-run/node";

// app/i18n/config.ts
var i18nConfig = {
  // 默認語言
  fallbackLng: "zh-TW",
  // 支援的語言
  supportedLngs: ["zh-TW", "en"],
  // 語言文件命名空間
  defaultNS: "common",
  // 啟用調試模式
  debug: !0,
  // 預加載的命名空間
  ns: ["common", "billing", "usage", "reports", "tariffs"],
  // 語言文件路徑
  backend: {
    loadPath: "/locales/{{lng}}/{{ns}}.json"
  },
  // 不允許使用不存在的 key
  saveMissing: !0,
  // 簡化嵌套的翻譯鍵
  keySeparator: ".",
  // 命名空間分隔符
  nsSeparator: ":",
  // 複數形式後綴分隔符
  pluralSeparator: "_",
  // 上下文分隔符
  contextSeparator: "_",
  // 插值配置
  interpolation: {
    escapeValue: !1,
    // React 已經處理了 XSS 防護
    formatSeparator: ","
  },
  // 檢測用戶語言
  detection: {
    order: ["querystring", "cookie", "localStorage", "navigator", "htmlTag"],
    caches: ["cookie"],
    lookupQuerystring: "lng",
    lookupCookie: "i18next",
    lookupLocalStorage: "i18nextLng"
  },
  // 啟用後端插件
  use: [],
  // 自定義語言別名
  nonExplicitSupportedLngs: !0,
  // 只加載語言代碼，不包含地區代碼
  load: "languageOnly",
  // 預設語言標籤
  cleanCode: !0,
  // 語言標籤標準化
  lowerCaseLng: !0,
  // 語言切換時重新加載頁面
  reloadOnPrerender: !0
}, config_default = i18nConfig;

// app/i18n/i18n.server.ts
var __filename = fileURLToPath(import.meta.url), __dirname = dirname(__filename), supportedLngs = config_default.supportedLngs, fallbackLng = config_default.fallbackLng, i18next = createInstance(), i18nextOptions = {
  ...config_default,
  backend: {
    loadPath: resolve(__dirname, "../../public/locales/{{lng}}/{{ns}}.json")
  },
  // 只加載語言代碼，不包含地區代碼
  load: "languageOnly"
};
i18next.use(initReactI18next).use(Backend).init(i18nextOptions);
var i18nCookie = createCookie("i18next", {
  path: "/",
  sameSite: "lax",
  httpOnly: !0,
  secure: !1
}), i18n = new RemixI18Next({
  detection: {
    supportedLanguages: supportedLngs,
    fallbackLanguage: fallbackLng,
    // 設置檢測順序
    order: ["searchParams", "cookie", "header"],
    // 設置 cookie 配置
    cookie: i18nCookie,
    // 設置搜索參數鍵名
    searchParamKey: "lng"
  },
  // 傳遞 i18next 配置選項
  i18next: i18nextOptions,
  // 添加後端插件
  backend: Backend
}), i18n_server_default = i18n;

// app/entry.server.tsx
import { jsxDEV } from "react/jsx-dev-runtime";
var ABORT_DELAY = 5e3;
async function handleRequest(request, responseStatusCode, responseHeaders, remixContext) {
  let callbackName = isbot(request.headers.get("user-agent")) ? "onAllReady" : "onShellReady", instance = createInstance2(), lng = await i18n_server_default.getLocale(request), ns = i18n_server_default.getRouteNamespaces(remixContext);
  return await instance.use(initReactI18next2).use(Backend2).init({
    ...config_default,
    lng,
    ns,
    backend: {
      loadPath: resolve2("./public/locales/{{lng}}/{{ns}}.json")
    }
  }), new Promise((resolve3, reject) => {
    let didError = !1, { pipe, abort } = renderToPipeableStream(
      /* @__PURE__ */ jsxDEV(I18nextProvider, { i18n: instance, children: /* @__PURE__ */ jsxDEV(RemixServer, { context: remixContext, url: request.url }, void 0, !1, {
        fileName: "app/entry.server.tsx",
        lineNumber: 51,
        columnNumber: 9
      }, this) }, void 0, !1, {
        fileName: "app/entry.server.tsx",
        lineNumber: 50,
        columnNumber: 7
      }, this),
      {
        [callbackName]() {
          let body = new PassThrough();
          responseHeaders.set("Content-Type", "text/html; charset=utf-8"), resolve3(
            new Response(body, {
              status: didError ? 500 : responseStatusCode,
              headers: responseHeaders
            })
          ), pipe(body);
        },
        onShellError(err) {
          reject(err);
        },
        onError(error) {
          didError = !0, console.error(error);
        }
      }
    );
    setTimeout(abort, ABORT_DELAY);
  });
}
export {
  handleRequest as default
};
//# sourceMappingURL=index.js.map
