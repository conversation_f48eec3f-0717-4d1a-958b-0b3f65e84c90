{"inputs": {"app/i18n/config.ts": {"bytes": 1924, "imports": [], "format": "esm"}, "app/i18n/i18n.server.ts": {"bytes": 1710, "imports": [{"path": "i18next", "kind": "import-statement", "external": true}, {"path": "react-i18next", "kind": "import-statement", "external": true}, {"path": "i18next-fs-backend", "kind": "import-statement", "external": true}, {"path": "node:path", "kind": "import-statement", "external": true}, {"path": "node:url", "kind": "import-statement", "external": true}, {"path": "node:path", "kind": "import-statement", "external": true}, {"path": "remix-i18next/server", "kind": "import-statement", "external": true}, {"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "app/i18n/config.ts", "kind": "import-statement", "original": "./config.js"}], "format": "esm"}, "app/entry.server.tsx": {"bytes": 2061, "imports": [{"path": "node:stream", "kind": "import-statement", "external": true}, {"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "isbot", "kind": "import-statement", "external": true}, {"path": "react-dom/server", "kind": "import-statement", "external": true}, {"path": "i18next", "kind": "import-statement", "external": true}, {"path": "react-i18next", "kind": "import-statement", "external": true}, {"path": "i18next-fs-backend", "kind": "import-statement", "external": true}, {"path": "node:path", "kind": "import-statement", "external": true}, {"path": "app/i18n/i18n.server.ts", "kind": "import-statement", "original": "./i18n/i18n.server"}, {"path": "app/i18n/config.ts", "kind": "import-statement", "original": "./i18n/config"}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}}, "outputs": {"build/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 9467}, "build/index.js": {"imports": [{"path": "node:stream", "kind": "import-statement", "external": true}, {"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "isbot", "kind": "import-statement", "external": true}, {"path": "react-dom/server", "kind": "import-statement", "external": true}, {"path": "i18next", "kind": "import-statement", "external": true}, {"path": "react-i18next", "kind": "import-statement", "external": true}, {"path": "i18next-fs-backend", "kind": "import-statement", "external": true}, {"path": "node:path", "kind": "import-statement", "external": true}, {"path": "i18next", "kind": "import-statement", "external": true}, {"path": "react-i18next", "kind": "import-statement", "external": true}, {"path": "i18next-fs-backend", "kind": "import-statement", "external": true}, {"path": "node:path", "kind": "import-statement", "external": true}, {"path": "node:url", "kind": "import-statement", "external": true}, {"path": "node:path", "kind": "import-statement", "external": true}, {"path": "remix-i18next/server", "kind": "import-statement", "external": true}, {"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "app/entry.server.tsx", "inputs": {"app/entry.server.tsx": {"bytesInOutput": 2103}, "app/i18n/i18n.server.ts": {"bytesInOutput": 1374}, "app/i18n/config.ts": {"bytesInOutput": 1365}}, "bytes": 5050}}}