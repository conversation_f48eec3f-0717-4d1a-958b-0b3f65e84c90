import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { api } from "~/utils/api";

// 處理 GET 請求 - 獲取所有電價方案
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const tariffPlans = await api.getTariffPlans();
    return json(tariffPlans);
  } catch (error) {
    console.error("獲取電價方案失敗:", error);
    return json(
      { error: "獲取電價方案時發生錯誤" },
      { status: 500 }
    );
  }
}

// 處理 POST 請求 - 新增電價方案
export async function action({ request }: ActionFunctionArgs) {
  try {
    if (request.method !== "POST") {
      return json(
        { error: "不支援的請求方法" },
        { status: 405 }
      );
    }

    const formData = await request.formData();
    const data = Object.fromEntries(formData);
    
    // 驗證數據
    if (!data.name || !data.rates) {
      return json(
        { error: "請填寫所有必填欄位" },
        { status: 400 }
      );
    }
    
    // 轉換數據類型
    const tariffPlan = {
      name: data.name.toString(),
      description: data.description?.toString() || "",
      rates: JSON.parse(data.rates.toString()), // 假設 rates 是 JSON 字符串
      effectiveDate: data.effectiveDate?.toString(),
      expirationDate: data.expirationDate?.toString(),
      isActive: data.isActive === "true"
    };
    
    // 調用後端 API 新增電價方案
    // 注意：這裡假設後端 API 有對應的方法，實際項目中需要根據後端 API 調整
    const result = await api.createTariffPlan(tariffPlan);
    return json(result);
    
  } catch (error) {
    console.error("新增電價方案失敗:", error);
    return json(
      { error: "新增電價方案時發生錯誤" },
      { status: 500 }
    );
  }
}
