import { json } from "@remix-run/node";
import { useLoaderData, useFetcher } from "@remix-run/react";
import { useState } from "react";
import { api } from "~/utils/api";

// 加載電價方案數據
export async function loader() {
  try {
    const tariffPlans = await api.getTariffPlans();
    return json({ tariffPlans });
  } catch (error) {
    console.error("加載電價方案失敗:", error);
    throw new Response("加載電價方案失敗", { status: 500 });
  }
}

export default function TariffsPage() {
  const { tariffPlans } = useLoaderData<typeof loader>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    rates: JSON.stringify([{ from: 0, to: 100, rate: 1.63 }], null, 2),
    effectiveDate: new Date().toISOString().split('T')[0],
    expirationDate: "",
    isActive: true
  });
  const fetcher = useFetcher();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    fetcher.submit(
      formData,
      { method: "post", action: "/api/tariffs" }
    );
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-800">電價方案管理</h2>
        <button
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          新增電價方案
        </button>
      </div>
      
      {/* 電價方案列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {tariffPlans?.map((plan: any) => (
            <li key={plan.id}>
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-blue-600 truncate">
                    {plan.name}
                  </p>
                  <div className="ml-2 flex-shrink-0 flex">
                    <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      plan.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {plan.isActive ? '啟用中' : '已停用'}
                    </p>
                  </div>
                </div>
                <div className="mt-2 sm:flex sm:justify-between">
                  <div className="sm:flex">
                    <p className="flex items-center text-sm text-gray-500">
                      {plan.description || '無描述'}
                    </p>
                  </div>
                  <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                    <p>
                      有效期限: {plan.effectiveDate ? new Date(plan.effectiveDate).toLocaleDateString('zh-TW') : '無'}
                      {plan.expirationDate && ` - ${new Date(plan.expirationDate).toLocaleDateString('zh-TW')}`}
                    </p>
                  </div>
                </div>
                <div className="mt-2">
                  <h4 className="text-sm font-medium text-gray-700">費率結構:</h4>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
                    {JSON.stringify(plan.rates, null, 2)}
                  </pre>
                </div>
              </div>
            </li>
          ))}
          {(!tariffPlans || tariffPlans.length === 0) && (
            <li className="px-4 py-4 text-center text-gray-500">
              尚無電價方案
            </li>
          )}
        </ul>
      </div>
      
      {/* 新增電價方案模態框 */}
      {isModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6">
              <div>
                <div className="mt-3 text-center sm:mt-5">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    新增電價方案
                  </h3>
                  <div className="mt-6 space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 text-left">
                        方案名稱 *
                      </label>
                      <input
                        type="text"
                        name="name"
                        id="name"
                        value={formData.name}
                        onChange={handleChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 text-left">
                        方案描述
                      </label>
                      <textarea
                        name="description"
                        id="description"
                        rows={2}
                        value={formData.description}
                        onChange={handleChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="rates" className="block text-sm font-medium text-gray-700 text-left">
                        費率結構 (JSON) *
                      </label>
                      <textarea
                        name="rates"
                        id="rates"
                        rows={6}
                        value={formData.rates}
                        onChange={handleChange}
                        className="mt-1 block w-full font-mono text-xs border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        範例: {`[{"from": 0, "to": 100, "rate": 1.63}, {"from": 101, "to": 300, "rate": 2.38}]`}
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="effectiveDate" className="block text-sm font-medium text-gray-700 text-left">
                          生效日期 *
                        </label>
                        <input
                          type="date"
                          name="effectiveDate"
                          id="effectiveDate"
                          value={formData.effectiveDate}
                          onChange={handleChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          required
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 text-left">
                          到期日期 (選填)
                        </label>
                        <input
                          type="date"
                          name="expirationDate"
                          id="expirationDate"
                          value={formData.expirationDate}
                          onChange={handleChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id="isActive"
                          name="isActive"
                          type="checkbox"
                          checked={formData.isActive}
                          onChange={handleChange}
                          className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="isActive" className="font-medium text-gray-700">
                          啟用此方案
                        </label>
                        <p className="text-gray-500">啟用後即可在計算電費時選擇此方案</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                <button
                  type="button"
                  onClick={handleSubmit}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm"
                >
                  儲存方案
                </button>
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
