import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import config from './config';

// 初始化 i18next
const initializeI18n = async () => {
  await i18n
    // 檢測用戶語言
    .use(LanguageDetector)
    // 使用 HTTP 後端加載翻譯文件
    .use(Backend)
    // 傳遞 i18n 實例到 react-i18next
    .use(initReactI18next)
    // 初始化 i18next
    .init({
      ...config,
      // 後端選項
      backend: {
        loadPath: '/locales/{{lng}}/{{ns}}.json',
        // 添加時間戳防止緩存
        queryStringParams: { v: process.env.APP_VERSION || '1.0.0' },
      },
      // 檢測選項
      detection: {
        ...config.detection,
        // 在開發環境下強制重新檢測語言
        caches: process.env.NODE_ENV === 'development' ? [] : config.detection?.caches || [],
      },
    });

  // 添加語言切換事件監聽
  if (typeof window !== 'undefined') {
    window.i18n = i18n;
  }

  return i18n;
};

// 導出已初始化的 i18n 實例
export default initializeI18n();

// 導出 i18n 實例
export { i18n };
