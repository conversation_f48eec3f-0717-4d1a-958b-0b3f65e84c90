@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定義基礎樣式 */
@layer base {
  body {
    @apply font-sans text-gray-900 antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold leading-tight tracking-tight;
  }
  
  h1 { @apply text-3xl md:text-4xl; }
  h2 { @apply text-2xl md:text-3xl; }
  h3 { @apply text-xl md:text-2xl; }
  h4 { @apply text-lg md:text-xl; }
  
  a {
    @apply text-blue-600 hover:text-blue-800 transition-colors duration-200;
  }
  
  /* 表單元素樣式 */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="date"],
  input[type="datetime-local"],
  input[type="time"],
  select,
  textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }
  
  /* 按鈕樣式 */
  button,
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
  }
  
  button:disabled,
  .btn:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  button.secondary,
  .btn.secondary {
    @apply bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
  }
  
  button.danger,
  .btn.danger {
    @apply bg-red-600 hover:bg-red-700 focus:ring-red-500;
  }
  
  /* 卡片樣式 */
  .card {
    @apply bg-white rounded-lg shadow overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
  }
  
  /* 表格樣式 */
  .table-container {
    @apply overflow-x-auto;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table thead th {
    @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table tbody tr {
    @apply bg-white;
  }
  
  .table tbody tr:nth-child(even) {
    @apply bg-gray-50;
  }
  
  .table tbody tr:hover {
    @apply bg-blue-50;
  }
  
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  
  /* 標籤樣式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
  
  /* 提示訊息 */
  .alert {
    @apply p-4 rounded-md border-l-4;
  }
  
  .alert-info {
    @apply bg-blue-50 border-blue-500 text-blue-700;
  }
  
  .alert-success {
    @apply bg-green-50 border-green-500 text-green-700;
  }
  
  .alert-warning {
    @apply bg-yellow-50 border-yellow-500 text-yellow-700;
  }
  
  .alert-danger {
    @apply bg-red-50 border-red-500 text-red-700;
  }
  
  /* 載入動畫 */
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  /* 響應式容器 */
  .container {
    @apply px-4 sm:px-6 lg:px-8 mx-auto max-w-7xl;
  }
  
  /* 頁面過渡動畫 */
  .page-enter {
    opacity: 0;
    transform: translateY(10px);
  }
  
  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms, transform 300ms;
  }
  
  .page-exit {
    opacity: 1;
    transform: translateY(0);
  }
  
  .page-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 300ms, transform 300ms;
  }
}
