{"version": 3, "sources": ["../app/entry.server.tsx", "../app/i18n/i18n.server.ts", "../app/i18n/config.ts"], "sourcesContent": ["import { PassThrough } from \"node:stream\";\nimport { Response } from \"@remix-run/node\";\nimport { RemixServer } from \"@remix-run/react\";\nimport isbot from \"isbot\";\nimport { renderToPipeableStream } from \"react-dom/server\";\nimport { createInstance } from \"i18next\";\nimport { I18nextProvider, initReactI18next } from \"react-i18next\";\nimport Backend from \"i18next-fs-backend\";\nimport { resolve } from \"node:path\";\nimport i18next from \"./i18n/i18n.server\";\nimport i18n from \"./i18n/config\";\n\ntype DocType = {\n  headers: Record<string, string>;\n  status?: number;\n  html: string;\n};\n\nconst ABORT_DELAY = 5000;\n\nexport default async function handleRequest(\n  request: Request,\n  responseStatusCode: number,\n  responseHeaders: Headers,\n  remixContext: any\n) {\n  const callbackName = isbot(request.headers.get(\"user-agent\"))\n    ? \"onAllReady\"\n    : \"onShellReady\";\n\n  const instance = createInstance();\n  const lng = await i18next.getLocale(request);\n  const ns = i18next.getRouteNamespaces(remixContext);\n\n  await instance\n    .use(initReactI18next)\n    .use(Backend)\n    .init({\n      ...i18n,\n      lng,\n      ns,\n      backend: {\n        loadPath: resolve(\"./public/locales/{{lng}}/{{ns}}.json\"),\n      },\n    });\n\n  return new Promise((resolve, reject) => {\n    let didError = false;\n    const { pipe, abort } = renderToPipeableStream(\n      <I18nextProvider i18n={instance}>\n        <RemixServer context={remixContext} url={request.url} />\n      </I18nextProvider>,\n      {\n        [callbackName]() {\n          const body = new PassThrough();\n          responseHeaders.set(\"Content-Type\", \"text/html; charset=utf-8\");\n          resolve(\n            new Response(body, {\n              status: didError ? 500 : responseStatusCode,\n              headers: responseHeaders,\n            })\n          );\n          pipe(body);\n        },\n        onShellError(err: unknown) {\n          reject(err);\n        },\n        onError(error: unknown) {\n          didError = true;\n          console.error(error);\n        },\n      }\n    );\n    setTimeout(abort, ABORT_DELAY);\n  });\n}\n", "import { createInstance } from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport Backend from 'i18next-fs-backend';\nimport { resolve } from 'node:path';\nimport { fileURLToPath } from 'node:url';\nimport { dirname } from 'node:path';\nimport { RemixI18Next } from 'remix-i18next/server';\nimport { createCookie } from '@remix-run/node';\nimport config from './config.js';\n\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = dirname(__filename);\n\n// 確保 supportedLngs 和 fallbackLng 有正確的類型\nconst supportedLngs = config.supportedLngs as string[];\nconst fallbackLng = config.fallbackLng as string;\n\n// 創建 i18next 實例\nexport const i18next = createInstance();\n\n// 初始化 i18next 的選項\nconst i18nextOptions = {\n  ...config,\n  backend: {\n    loadPath: resolve(__dirname, '../../public/locales/{{lng}}/{{ns}}.json'),\n  },\n  // 只加載語言代碼，不包含地區代碼\n  load: 'languageOnly' as const,\n};\n\n// 初始化 i18next 實例\ni18next\n  .use(initReactI18next)\n  .use(Backend)\n  .init(i18nextOptions);\n\n// 創建語言 cookie\nconst i18nCookie = createCookie('i18next', {\n  path: '/',\n  sameSite: 'lax',\n  httpOnly: true,\n  secure: process.env.NODE_ENV === 'production',\n});\n\n// 創建 RemixI18Next 實例\nconst i18n = new RemixI18Next({\n  detection: {\n    supportedLanguages: supportedLngs,\n    fallbackLanguage: fallbackLng,\n    // 設置檢測順序\n    order: ['searchParams', 'cookie', 'header'],\n    // 設置 cookie 配置\n    cookie: i18nCookie,\n    // 設置搜索參數鍵名\n    searchParamKey: 'lng',\n  },\n  // 傳遞 i18next 配置選項\n  i18next: i18nextOptions,\n  // 添加後端插件\n  backend: Backend,\n});\n\nexport default i18n;\n", "import type { InitOptions } from 'i18next';\n\n// Create a custom type that extends InitOptions with our custom properties\ninterface CustomInitOptions extends Omit<InitOptions, 'use' | 'nonExplicitSupportedLngs' | 'cleanCode' | 'lowerCaseLng' | 'reloadOnPrerender'> {\n  nonExplicitSupportedLngs?: boolean;\n  cleanCode?: boolean;\n  lowerCaseLng?: boolean;\n  reloadOnPrerender?: boolean;\n}\n\nconst i18nConfig: CustomInitOptions = {\n  // 默認語言\n  fallbackLng: 'zh-TW',\n  \n  // 支援的語言\n  supportedLngs: ['zh-TW', 'en'],\n  \n  // 語言文件命名空間\n  defaultNS: 'common',\n  \n  // 啟用調試模式\n  debug: process.env.NODE_ENV === 'development',\n  \n  // 預加載的命名空間\n  ns: ['common', 'billing', 'usage', 'reports', 'tariffs'] as const,\n  \n  // 語言文件路徑\n  backend: {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n  },\n  \n  // 不允許使用不存在的 key\n  saveMissing: true,\n  \n  // 簡化嵌套的翻譯鍵\n  keySeparator: '.',\n  \n  // 命名空間分隔符\n  nsSeparator: ':',\n  \n  // 複數形式後綴分隔符\n  pluralSeparator: '_',\n  \n  // 上下文分隔符\n  contextSeparator: '_',\n  \n  // 插值配置\n  interpolation: {\n    escapeValue: false, // React 已經處理了 XSS 防護\n    formatSeparator: ',',\n  },\n  \n  // 檢測用戶語言\n  detection: {\n    order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],\n    caches: ['cookie'],\n    lookupQuerystring: 'lng',\n    lookupCookie: 'i18next',\n    lookupLocalStorage: 'i18nextLng',\n  },\n  \n  // 啟用後端插件\n  use: [],\n  \n  // 自定義語言別名\n  nonExplicitSupportedLngs: true,\n  // 只加載語言代碼，不包含地區代碼\n  load: 'languageOnly' as const,\n  \n  // 預設語言標籤\n  cleanCode: true,\n  \n  // 語言標籤標準化\n  lowerCaseLng: true,\n  \n  // 語言切換時重新加載頁面\n  reloadOnPrerender: process.env.NODE_ENV === 'development',\n};\n\nexport default i18nConfig;\n"], "mappings": ";AAAA,SAAS,mBAAmB;AAC5B,SAAS,gBAAgB;AACzB,SAAS,mBAAmB;AAC5B,OAAO,WAAW;AAClB,SAAS,8BAA8B;AACvC,SAAS,kBAAAA,uBAAsB;AAC/B,SAAS,iBAAiB,oBAAAC,yBAAwB;AAClD,OAAOC,cAAa;AACpB,SAAS,WAAAC,gBAAe;;;ACRxB,SAAS,sBAAsB;AAC/B,SAAS,wBAAwB;AACjC,OAAO,aAAa;AACpB,SAAS,eAAe;AACxB,SAAS,qBAAqB;AAC9B,SAAS,eAAe;AACxB,SAAS,oBAAoB;AAC7B,SAAS,oBAAoB;;;ACG7B,IAAM,aAAgC;AAAA;AAAA,EAEpC,aAAa;AAAA;AAAA,EAGb,eAAe,CAAC,SAAS,IAAI;AAAA;AAAA,EAG7B,WAAW;AAAA;AAAA,EAGX,OAAO;AAAA;AAAA,EAGP,IAAI,CAAC,UAAU,WAAW,SAAS,WAAW,SAAS;AAAA;AAAA,EAGvD,SAAS;AAAA,IACP,UAAU;AAAA,EACZ;AAAA;AAAA,EAGA,aAAa;AAAA;AAAA,EAGb,cAAc;AAAA;AAAA,EAGd,aAAa;AAAA;AAAA,EAGb,iBAAiB;AAAA;AAAA,EAGjB,kBAAkB;AAAA;AAAA,EAGlB,eAAe;AAAA,IACb,aAAa;AAAA;AAAA,IACb,iBAAiB;AAAA,EACnB;AAAA;AAAA,EAGA,WAAW;AAAA,IACT,OAAO,CAAC,eAAe,UAAU,gBAAgB,aAAa,SAAS;AAAA,IACvE,QAAQ,CAAC,QAAQ;AAAA,IACjB,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,oBAAoB;AAAA,EACtB;AAAA;AAAA,EAGA,KAAK,CAAC;AAAA;AAAA,EAGN,0BAA0B;AAAA;AAAA,EAE1B,MAAM;AAAA;AAAA,EAGN,WAAW;AAAA;AAAA,EAGX,cAAc;AAAA;AAAA,EAGd,mBAAmB;AACrB,GAEO,iBAAQ;;;ADrEf,IAAM,aAAa,cAAc,YAAY,GAAG,GAC1C,YAAY,QAAQ,UAAU,GAG9B,gBAAgB,eAAO,eACvB,cAAc,eAAO,aAGd,UAAU,eAAe,GAGhC,iBAAiB;AAAA,EACrB,GAAG;AAAA,EACH,SAAS;AAAA,IACP,UAAU,QAAQ,WAAW,0CAA0C;AAAA,EACzE;AAAA;AAAA,EAEA,MAAM;AACR;AAGA,QACG,IAAI,gBAAgB,EACpB,IAAI,OAAO,EACX,KAAK,cAAc;AAGtB,IAAM,aAAa,aAAa,WAAW;AAAA,EACzC,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AACV,CAAC,GAGK,OAAO,IAAI,aAAa;AAAA,EAC5B,WAAW;AAAA,IACT,oBAAoB;AAAA,IACpB,kBAAkB;AAAA;AAAA,IAElB,OAAO,CAAC,gBAAgB,UAAU,QAAQ;AAAA;AAAA,IAE1C,QAAQ;AAAA;AAAA,IAER,gBAAgB;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,SAAS;AACX,CAAC,GAEM,sBAAQ;;;ADZP;AAhCR,IAAM,cAAc;AAEpB,eAAO,cACL,SACA,oBACA,iBACA,cACA;AACA,MAAM,eAAe,MAAM,QAAQ,QAAQ,IAAI,YAAY,CAAC,IACxD,eACA,gBAEE,WAAWC,gBAAe,GAC1B,MAAM,MAAM,oBAAQ,UAAU,OAAO,GACrC,KAAK,oBAAQ,mBAAmB,YAAY;AAElD,eAAM,SACH,IAAIC,iBAAgB,EACpB,IAAIC,QAAO,EACX,KAAK;AAAA,IACJ,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,SAAS;AAAA,MACP,UAAUC,SAAQ,sCAAsC;AAAA,IAC1D;AAAA,EACF,CAAC,GAEI,IAAI,QAAQ,CAACA,UAAS,WAAW;AACtC,QAAI,WAAW,IACT,EAAE,MAAM,MAAM,IAAI;AAAA,MACtB,uBAAC,mBAAgB,MAAM,UACrB,iCAAC,eAAY,SAAS,cAAc,KAAK,QAAQ,OAAjD;AAAA;AAAA;AAAA;AAAA,aAAsD,KADxD;AAAA;AAAA;AAAA;AAAA,aAEA;AAAA,MACA;AAAA,QACE,CAAC,YAAY,IAAI;AACf,cAAM,OAAO,IAAI,YAAY;AAC7B,0BAAgB,IAAI,gBAAgB,0BAA0B,GAC9DA;AAAA,YACE,IAAI,SAAS,MAAM;AAAA,cACjB,QAAQ,WAAW,MAAM;AAAA,cACzB,SAAS;AAAA,YACX,CAAC;AAAA,UACH,GACA,KAAK,IAAI;AAAA,QACX;AAAA,QACA,aAAa,KAAc;AACzB,iBAAO,GAAG;AAAA,QACZ;AAAA,QACA,QAAQ,OAAgB;AACtB,qBAAW,IACX,QAAQ,MAAM,KAAK;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,eAAW,OAAO,WAAW;AAAA,EAC/B,CAAC;AACH;", "names": ["createInstance", "initReactI18next", "Backend", "resolve", "createInstance", "initReactI18next", "Backend", "resolve"]}