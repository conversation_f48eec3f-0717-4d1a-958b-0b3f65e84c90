import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-fs-backend';
import { resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import { dirname } from 'node:path';
import { RemixI18Next } from 'remix-i18next/server';
import { createCookie } from '@remix-run/node';
import config from './config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 確保 supportedLngs 和 fallbackLng 有正確的類型
const supportedLngs = config.supportedLngs as string[];
const fallbackLng = config.fallbackLng as string;

// 創建 i18next 實例
export const i18next = createInstance();

// 初始化 i18next 的選項
const i18nextOptions = {
  ...config,
  backend: {
    loadPath: resolve(__dirname, '../../public/locales/{{lng}}/{{ns}}.json'),
  },
  // 只加載語言代碼，不包含地區代碼
  load: 'languageOnly' as const,
};

// 初始化 i18next 實例
i18next
  .use(initReactI18next)
  .use(Backend)
  .init(i18nextOptions);

// 創建語言 cookie
const i18nCookie = createCookie('i18next', {
  path: '/',
  sameSite: 'lax',
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
});

// 創建 RemixI18Next 實例
const i18n = new RemixI18Next({
  detection: {
    supportedLanguages: supportedLngs,
    fallbackLanguage: fallbackLng,
    // 設置檢測順序
    order: ['searchParams', 'cookie', 'header'],
    // 設置 cookie 配置
    cookie: i18nCookie,
    // 設置搜索參數鍵名
    searchParamKey: 'lng',
  },
  // 傳遞 i18next 配置選項
  i18next: i18nextOptions,
  // 添加後端插件
  backend: Backend,
});

export default i18n;
