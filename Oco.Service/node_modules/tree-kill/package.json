{"name": "tree-kill", "version": "1.2.2", "description": "kill trees of processes", "main": "index.js", "types": "index.d.ts", "bin": {"tree-kill": "cli.js"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "license": "MIT", "devDependencies": {"mocha": "^2.2.5"}}