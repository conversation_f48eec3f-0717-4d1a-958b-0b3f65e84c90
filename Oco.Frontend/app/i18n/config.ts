import type { InitOptions } from 'i18next';

// Create a custom type that extends InitOptions with our custom properties
interface CustomInitOptions extends Omit<InitOptions, 'use' | 'nonExplicitSupportedLngs' | 'cleanCode' | 'lowerCaseLng' | 'reloadOnPrerender'> {
  nonExplicitSupportedLngs?: boolean;
  cleanCode?: boolean;
  lowerCaseLng?: boolean;
  reloadOnPrerender?: boolean;
}

const i18nConfig: CustomInitOptions = {
  // 默認語言
  fallbackLng: 'zh-TW',
  
  // 支援的語言
  supportedLngs: ['zh-TW', 'en'],
  
  // 語言文件命名空間
  defaultNS: 'common',
  
  // 啟用調試模式
  debug: process.env.NODE_ENV === 'development',
  
  // 預加載的命名空間
  ns: ['common', 'billing', 'usage', 'reports', 'tariffs'] as const,
  
  // 語言文件路徑
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  },
  
  // 不允許使用不存在的 key
  saveMissing: true,
  
  // 簡化嵌套的翻譯鍵
  keySeparator: '.',
  
  // 命名空間分隔符
  nsSeparator: ':',
  
  // 複數形式後綴分隔符
  pluralSeparator: '_',
  
  // 上下文分隔符
  contextSeparator: '_',
  
  // 插值配置
  interpolation: {
    escapeValue: false, // React 已經處理了 XSS 防護
    formatSeparator: ',',
  },
  
  // 檢測用戶語言
  detection: {
    order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],
    caches: ['cookie'],
    lookupQuerystring: 'lng',
    lookupCookie: 'i18next',
    lookupLocalStorage: 'i18nextLng',
  },
  
  // 啟用後端插件
  use: [],
  
  // 自定義語言別名
  nonExplicitSupportedLngs: true,
  // 只加載語言代碼，不包含地區代碼
  load: 'languageOnly' as const,
  
  // 預設語言標籤
  cleanCode: true,
  
  // 語言標籤標準化
  lowerCaseLng: true,
  
  // 語言切換時重新加載頁面
  reloadOnPrerender: process.env.NODE_ENV === 'development',
};

export default i18nConfig;
