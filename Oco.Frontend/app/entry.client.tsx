import { RemixBrowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
import { I18nextProvider } from "react-i18next";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import i18n from "./i18n/i18n.client";

// 建立 QueryClient 實例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 分鐘
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// 延遲 hydration 以優化性能
function hydrate() {
  startTransition(() => {
    hydrateRoot(
      document,
      <StrictMode>
        <QueryClientProvider client={queryClient}>
          <I18nextProvider i18n={i18n}>
            <RemixBrowser />
          </I18nextProvider>
        </QueryClientProvider>
      </StrictMode>
    );
  });
}

// 等待頁面完全加載後再進行 hydration
if (window.requestIdleCallback) {
  window.requestIdleCallback(hydrate);
} else {
  // 不支援 requestIdleCallback 的瀏覽器
  setTimeout(hydrate, 1);
}

// 註冊 Service Worker 用於離線支援
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js').then(
      (registration) => {
        console.log('ServiceWorker 註冊成功: ', registration.scope);
      },
      (err) => {
        console.log('ServiceWorker 註冊失敗: ', err);
      }
    );
  });
}
