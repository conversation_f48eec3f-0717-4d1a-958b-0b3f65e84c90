import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 請求攔截器
    this.client.interceptors.request.use(
      (config) => {
        // 可以在此處添加認證 token
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 回應攔截器
    this.client.interceptors.response.use(
      (response) => response.data,
      (error) => {
        // 統一錯誤處理
        if (error.response) {
          console.error('API Error:', error.response.data);
          return Promise.reject({
            status: error.response.status,
            message: error.response.data?.message || '發生未知錯誤',
          });
        }
        return Promise.reject({
          status: 500,
          message: '無法連接到伺服器',
        });
      }
    );
  }

  // 用電量相關 API
  async getUsageRecords(params = {}) {
    return this.client.get('/usage', { params });
  }

  async createUsageRecord(data: any) {
    return this.client.post('/usage', data);
  }

  // 電價相關 API
  async getTariffPlans() {
    return this.client.get('/tariff-plans');
  }

  // 帳單相關 API
  async calculateBill(usageId: string, tariffPlanId: string) {
    return this.client.post('/billing/calculate', { usageId, tariffPlanId });
  }

  async getBillingHistory(params = {}) {
    return this.client.get('/billing/history', { params });
  }

  // 報表相關 API
  async generateReport(params: any) {
    return this.client.get('/reports', { params });
  }
}

export const api = new ApiClient();
